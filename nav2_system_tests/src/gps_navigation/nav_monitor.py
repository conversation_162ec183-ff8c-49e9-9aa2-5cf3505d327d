#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
导航系统监控节点

持续监控Navigation2系统的关键组件状态，检查所有必需的导航节点的生命周期状态。
"""

import rclpy
from rclpy.node import Node
from lifecycle_msgs.srv import GetState
from std_srvs.srv import Trigger


class NavMonitor(Node):
    def __init__(self):
        super().__init__('nav_monitor')

        # 参数 - 与启动文件中的lifecycle_nodes顺序保持一致
        self.declare_parameter('required_nodes', [
            '/controller_server',
            '/smoother_server',
            '/planner_server',
            '/behavior_server',
            '/velocity_smoother',
            '/collision_monitor',
            '/bt_navigator',
            '/waypoint_follower'
        ])
        self.declare_parameter('check_frequency', 1.0)
        self.declare_parameter('service_timeout', 2.0)

        self.required_nodes = self.get_parameter('required_nodes').value
        self.check_frequency = self.get_parameter('check_frequency').value
        self.service_timeout = self.get_parameter('service_timeout').value

        # 导航系统状态跟踪
        self.node_status = {node: False for node in self.required_nodes}
        self.node_lifecycle_status = {node: False for node in self.required_nodes}  # 生命周期状态跟踪

        # 状态标志
        self.all_nav_ready = False
        self.exit_when_ready = True  # 导航就绪时自动退出

        # 稳定性检查
        self.stability_check_duration = 3.0  # 检测到就绪后等待3秒确保稳定
        self.first_ready_time = None  # 首次检测到就绪的时间

        # 定时器 - 定期检查导航系统状态
        self.timer = self.create_timer(1.0 / self.check_frequency, self.check_navigation_status)

        # 生命周期状态客户端缓存
        self.lifecycle_clients = {}

        # 生命周期管理器状态客户端缓存
        self.lifecycle_manager_clients = {}

        self.get_logger().info(f'Navigation monitor started.')
        self.get_logger().info(f'Required nodes: {len(self.required_nodes)}')
        self.get_logger().info(f'Check frequency: {self.check_frequency} Hz')

    def check_lifecycle_state(self, node_name):
        """检查生命周期节点的状态是否为 active"""
        try:
            # 确保节点名称以 '/' 开头
            if not node_name.startswith('/'):
                node_name = '/' + node_name

            # 创建或获取生命周期状态客户端
            if node_name not in self.lifecycle_clients:
                service_name = f'{node_name}/get_state'
                self.lifecycle_clients[node_name] = self.create_client(GetState, service_name)

            client = self.lifecycle_clients[node_name]

            # 检查服务是否可用（短超时）
            if not client.wait_for_service(timeout_sec=0.5):
                self.get_logger().debug(f'Lifecycle service {service_name} not available')
                return False

            # 创建请求
            request = GetState.Request()

            # 同步调用服务（短超时）
            try:
                future = client.call_async(request)
                # 使用 spin_until_future_complete 但设置短超时
                rclpy.spin_until_future_complete(self, future, timeout_sec=1.0)

                if future.done():
                    response = future.result()
                    # 检查状态是否为 active (状态ID为3)
                    is_active = response.current_state.id == 3  # ACTIVE state
                    if is_active:
                        self.get_logger().debug(f'Node {node_name} lifecycle state: {response.current_state.label} (id={response.current_state.id})')
                    else:
                        self.get_logger().info(f'⚠️ Node {node_name} lifecycle state: {response.current_state.label} (id={response.current_state.id}) - not active')
                    return is_active
                else:
                    self.get_logger().debug(f'Timeout checking lifecycle state for {node_name}')
                    return False
            except Exception as e:
                self.get_logger().debug(f'Exception checking lifecycle state for {node_name}: {e}')
                return False

        except Exception as e:
            # 如果出现任何错误，假设节点不是生命周期节点
            # 对于非生命周期节点，我们无法检查其状态，因此返回False要求明确的生命周期支持
            self.get_logger().debug(f'Node {node_name} may not be a lifecycle node: {e}')
            return False

    def check_nodes(self):
        """检查所有必需的节点是否运行并且处于正确的生命周期状态"""
        node_names = self.get_node_names()

        # 调试信息：打印一次节点列表
        if not hasattr(self, '_debug_printed'):
            self.get_logger().info(f'Available nodes: {node_names}')
            self._debug_printed = True

        for node in self.required_nodes:
            # 移除前导斜杠进行比较
            node_name = node.lstrip('/')
            is_running = node_name in node_names

            # 检查节点运行状态
            if not is_running:
                self.get_logger().debug(f'Node {node_name} not found in node list')

            # 检查生命周期状态（如果节点正在运行）
            lifecycle_active = False
            if is_running:
                lifecycle_active = self.check_lifecycle_state(node)

            # 节点需要同时运行且生命周期状态为active才算就绪
            is_ready = is_running and lifecycle_active

            # 更新节点运行状态
            if is_running != self.node_status[node]:
                self.node_status[node] = is_running
                if is_running:
                    self.get_logger().info(f'✅ Node {node} is now RUNNING')
                else:
                    self.get_logger().warning(f'❌ Node {node} is NOT RUNNING')

            # 更新生命周期状态
            if lifecycle_active != self.node_lifecycle_status[node]:
                self.node_lifecycle_status[node] = lifecycle_active
                if lifecycle_active:
                    self.get_logger().info(f'🔄 Node {node} lifecycle is now ACTIVE')
                else:
                    if is_running:  # 只有在节点运行时才报告生命周期问题
                        self.get_logger().warning(f'⚠️ Node {node} lifecycle is NOT ACTIVE')

            # 调试信息：节点仍然未完全就绪
            if not is_ready and is_running:
                self.get_logger().debug(f'Node {node_name} running but lifecycle not active')
            elif not is_ready and not is_running:
                self.get_logger().debug(f'Node {node_name} not running')

    def check_navigation_status(self):
        """定期检查导航系统状态"""
        # 检查节点状态
        self.check_nodes()

        # 检查是否所有组件都就绪
        all_nodes_running = all(self.node_status.values())
        all_nodes_lifecycle_active = all(self.node_lifecycle_status.values())
        all_ready = all_nodes_running and all_nodes_lifecycle_active

        current_time = self.get_clock().now()

        if all_ready:
            if self.first_ready_time is None:
                # 首次检测到所有组件就绪
                self.first_ready_time = current_time
                self.get_logger().info('🔄 All navigation components detected as ready. Starting stability check...')
            elif not self.all_nav_ready:
                # 检查是否已经稳定足够长时间
                elapsed_time = (current_time - self.first_ready_time).nanoseconds / 1e9
                if elapsed_time >= self.stability_check_duration:
                    # 系统已稳定
                    self.all_nav_ready = True
                    self.get_logger().info(f'✅ ALL NAVIGATION COMPONENTS ARE STABLE FOR {elapsed_time:.1f}s!')

                    # 如果设置为导航就绪时退出，则退出节点
                    if self.exit_when_ready:
                        # 延迟退出
                        self.create_timer(1.0, self.exit_node)
                else:
                    # 仍在稳定性检查期间
                    remaining_time = self.stability_check_duration - elapsed_time
                    self.get_logger().info(f'⏳ Stability check in progress... {remaining_time:.1f}s remaining')
                    # 定期显示详细状态
                    if int(elapsed_time) % 2 == 0:  # 每2秒显示一次详细状态
                        self.log_detailed_status()
        else:
            # 有组件不就绪，重置稳定性检查
            if self.first_ready_time is not None:
                self.get_logger().warning('⚠️ Components became unavailable during stability check. Resetting...')
                self.first_ready_time = None

            if self.all_nav_ready:
                # 有组件失效
                self.all_nav_ready = False
                not_running_nodes = [name for name, status in self.node_status.items() if not status]
                not_active_lifecycle_nodes = [name for name, status in self.node_lifecycle_status.items() if not status]

                if not_running_nodes:
                    self.get_logger().warning(f'Nodes not running: {not_running_nodes}')
                if not_active_lifecycle_nodes:
                    self.get_logger().warning(f'Nodes lifecycle not active: {not_active_lifecycle_nodes}')
                if not self.lifecycle_configured:
                    self.get_logger().warning(f'Lifecycle manager not configured')

    def log_detailed_status(self):
        """记录详细的系统状态信息"""
        running_count = sum(self.node_status.values())
        active_count = sum(self.node_lifecycle_status.values())

        self.get_logger().info(f'📊 Status: Nodes Running({running_count}/{len(self.required_nodes)}) '
                              f'Active({active_count}/{len(self.required_nodes)}) '
                              f'Lifecycle Manager({"✓" if self.lifecycle_configured else "✗"})')

        # 显示未就绪的组件
        not_running = [name.split('/')[-1] for name, status in self.node_status.items() if not status]
        not_active = [name.split('/')[-1] for name, status in self.node_lifecycle_status.items() if not status]

        if not_running:
            self.get_logger().info(f'🔴 Not Running: {", ".join(not_running)}')
        if not_active:
            self.get_logger().info(f'🟡 Not Active: {", ".join(not_active)}')



    def exit_node(self):
        """退出节点以触发OnExecutionComplete事件"""
        self.get_logger().info('✅ Navigation monitor task completed. Exiting...')
        # 停止定时器
        if hasattr(self, 'timer'):
            self.timer.cancel()
        # 触发节点退出
        import sys
        sys.exit(0)


def main(args=None):
    rclpy.init(args=args)

    nav_monitor = NavMonitor()

    try:
        rclpy.spin(nav_monitor)
    except KeyboardInterrupt:
        pass
    finally:
        nav_monitor.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
